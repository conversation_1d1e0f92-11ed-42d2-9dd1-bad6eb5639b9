import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/admin/featured/[id]
 * Get a specific featured content by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Validate featured content ID
    if (!mongoose.default.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid featured content ID' }, { status: 400 });
    }

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the Content schema directly
    const ContentSchema = new mongoose.default.Schema({
      title: String,
      type: String,
      posterPath: String,
      backdropPath: String,
      year: Number,
      genres: [String],
      featured: Boolean
    }, {
      timestamps: true
    });

    // Get the Content model
    const Content = mongoose.default.models.Content ||
                   mongoose.default.model('Content', ContentSchema);

    // Define the FeaturedContent schema directly
    const FeaturedContentSchema = new mongoose.default.Schema({
      contentId: {
        type: mongoose.default.Schema.Types.ObjectId,
        ref: 'Content'
      },
      order: Number,
      startDate: Date,
      endDate: Date
    }, {
      timestamps: true
    });

    // Get the FeaturedContent model
    const FeaturedContent = mongoose.default.models.FeaturedContent ||
                           mongoose.default.model('FeaturedContent', FeaturedContentSchema);

    // Find featured content
    const featuredContent = await FeaturedContent.findById(params.id)
      .populate('contentId', 'title type posterPath backdropPath year genres');

    if (!featuredContent) {
      return NextResponse.json({ error: 'Featured content not found' }, { status: 404 });
    }

    // Return featured content
    return NextResponse.json({
      id: featuredContent._id.toString(),
      contentId: featuredContent.contentId._id.toString(),
      title: (featuredContent.contentId as any).title,
      type: (featuredContent.contentId as any).type,
      posterPath: (featuredContent.contentId as any).posterPath,
      backdropPath: (featuredContent.contentId as any).backdropPath,
      year: (featuredContent.contentId as any).year,
      genres: (featuredContent.contentId as any).genres,
      order: featuredContent.order,
      startDate: featuredContent.startDate,
      endDate: featuredContent.endDate
    });
  } catch (error) {
    console.error('Error fetching featured content:', error);
    return NextResponse.json(
      { error: 'Failed to fetch featured content', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/featured/[id]
 * Update a specific featured content
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Validate featured content ID
    if (!mongoose.default.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid featured content ID' }, { status: 400 });
    }

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the Content schema directly
    const ContentSchema = new mongoose.default.Schema({
      title: String,
      type: String,
      posterPath: String,
      backdropPath: String,
      year: Number,
      genres: [String],
      featured: Boolean
    }, {
      timestamps: true
    });

    // Get the Content model
    const Content = mongoose.default.models.Content ||
                   mongoose.default.model('Content', ContentSchema);

    // Define the FeaturedContent schema directly
    const FeaturedContentSchema = new mongoose.default.Schema({
      contentId: {
        type: mongoose.default.Schema.Types.ObjectId,
        ref: 'Content'
      },
      order: Number,
      startDate: Date,
      endDate: Date
    }, {
      timestamps: true
    });

    // Get the FeaturedContent model
    const FeaturedContent = mongoose.default.models.FeaturedContent ||
                           mongoose.default.model('FeaturedContent', FeaturedContentSchema);

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Get request data
    const data = await request.json();

    // Find featured content first to get its content ID for logging
    const featuredContent = await FeaturedContent.findById(params.id)
      .populate('contentId', 'title');

    if (!featuredContent) {
      return NextResponse.json({ error: 'Featured content not found' }, { status: 404 });
    }

    // Update featured content
    const updatedFeaturedContent = await FeaturedContent.findByIdAndUpdate(
      params.id,
      {
        order: data.order,
        startDate: data.startDate,
        endDate: data.endDate
      },
      { new: true }
    ).populate('contentId', 'title type posterPath backdropPath year genres');

    // Log admin activity directly
    await UserActivity.create({
      userId: new mongoose.default.Types.ObjectId(userId),
      type: 'admin',
      action: 'update_featured_content',
      details: `Admin updated featured content: ${(featuredContent.contentId as any).title}`,
      ipAddress: request.ip || request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      timestamp: new Date(),
      metadata: { featuredContentId: params.id }
    });

    // Return updated featured content
    return NextResponse.json({
      id: updatedFeaturedContent._id.toString(),
      contentId: updatedFeaturedContent.contentId._id.toString(),
      title: (updatedFeaturedContent.contentId as any).title,
      type: (updatedFeaturedContent.contentId as any).type,
      posterPath: (updatedFeaturedContent.contentId as any).posterPath,
      backdropPath: (updatedFeaturedContent.contentId as any).backdropPath,
      year: (updatedFeaturedContent.contentId as any).year,
      genres: (updatedFeaturedContent.contentId as any).genres,
      order: updatedFeaturedContent.order,
      startDate: updatedFeaturedContent.startDate,
      endDate: updatedFeaturedContent.endDate
    });
  } catch (error) {
    console.error('Error updating featured content:', error);
    return NextResponse.json(
      { error: 'Failed to update featured content', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/featured/[id]
 * Remove content from featured
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Validate featured content ID
    if (!mongoose.default.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid featured content ID' }, { status: 400 });
    }

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the Content schema directly
    const ContentSchema = new mongoose.default.Schema({
      title: String,
      type: String,
      posterPath: String,
      backdropPath: String,
      year: Number,
      genres: [String],
      featured: Boolean
    }, {
      timestamps: true
    });

    // Get the Content model
    const Content = mongoose.default.models.Content ||
                   mongoose.default.model('Content', ContentSchema);

    // Define the FeaturedContent schema directly
    const FeaturedContentSchema = new mongoose.default.Schema({
      contentId: {
        type: mongoose.default.Schema.Types.ObjectId,
        ref: 'Content'
      },
      order: Number,
      startDate: Date,
      endDate: Date
    }, {
      timestamps: true
    });

    // Get the FeaturedContent model
    const FeaturedContent = mongoose.default.models.FeaturedContent ||
                           mongoose.default.model('FeaturedContent', FeaturedContentSchema);

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Find featured content first to get its content ID for logging
    const featuredContent = await FeaturedContent.findById(params.id)
      .populate('contentId', 'title');

    if (!featuredContent) {
      return NextResponse.json({ error: 'Featured content not found' }, { status: 404 });
    }

    // Delete featured content
    await FeaturedContent.findByIdAndDelete(params.id);

    // Update content to mark as not featured
    await Content.findByIdAndUpdate(featuredContent.contentId, { featured: false });

    // Log admin activity directly
    await UserActivity.create({
      userId: new mongoose.default.Types.ObjectId(userId),
      type: 'admin',
      action: 'remove_featured_content',
      details: `Admin removed content from featured: ${(featuredContent.contentId as any).title}`,
      ipAddress: request.ip || request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      timestamp: new Date(),
      metadata: { contentId: featuredContent.contentId.toString() }
    });

    // Return success
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error removing featured content:', error);
    return NextResponse.json(
      { error: 'Failed to remove featured content', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
