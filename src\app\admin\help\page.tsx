'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  MessageSquare, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  TrendingUp,
  Users,
  Star,
  Search,
  Filter,
  Eye,
  Calendar,
  BarChart3
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';

interface HelpStats {
  overview: {
    totalTickets: number;
    openTickets: number;
    inProgressTickets: number;
    resolvedTickets: number;
    closedTickets: number;
    escalatedTickets: number;
    recentTickets: number;
    recentResolved: number;
    averageResolutionTime: number;
  };
  ticketsByCategory: Array<{
    _id: string;
    count: number;
    open: number;
    resolved: number;
  }>;
  ticketsByPriority: Array<{
    _id: string;
    count: number;
  }>;
  topAgents: Array<{
    _id: string;
    assignedToName: string;
    totalTickets: number;
    resolvedTickets: number;
  }>;
  satisfaction: {
    averageRating: number;
    totalRatings: number;
    distribution: Record<string, number>;
  };
}

interface Ticket {
  _id: string;
  ticketNumber: string;
  subject: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in_progress' | 'waiting_for_user' | 'resolved' | 'closed';
  createdAt: string;
  updatedAt: string;
  userId: {
    name: string;
    email: string;
  };
  assignedTo?: {
    name: string;
  };
  responses: string[];
  escalated: boolean;
}

const statusConfig = {
  open: { label: 'Open', color: 'bg-blue-500/20 text-blue-400', icon: AlertCircle },
  in_progress: { label: 'In Progress', color: 'bg-yellow-500/20 text-yellow-400', icon: Clock },
  waiting_for_user: { label: 'Waiting for User', color: 'bg-orange-500/20 text-orange-400', icon: MessageSquare },
  resolved: { label: 'Resolved', color: 'bg-green-500/20 text-green-400', icon: CheckCircle },
  closed: { label: 'Closed', color: 'bg-gray-500/20 text-gray-400', icon: CheckCircle }
};

const priorityConfig = {
  low: { label: 'Low', color: 'bg-gray-500/20 text-gray-400' },
  medium: { label: 'Medium', color: 'bg-blue-500/20 text-blue-400' },
  high: { label: 'High', color: 'bg-orange-500/20 text-orange-400' },
  urgent: { label: 'Urgent', color: 'bg-red-500/20 text-red-400' }
};

export default function AdminHelpPage() {
  const { user } = useAuth();
  const [stats, setStats] = useState<HelpStats | null>(null);
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [loading, setLoading] = useState(true);
  const [ticketsLoading, setTicketsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchStats();
    fetchTickets();
  }, []);

  useEffect(() => {
    fetchTickets();
  }, [page, statusFilter, priorityFilter, searchQuery]);

  const fetchStats = async () => {
    try {
      const response = await fetch(`/api/help/stats?userId=${user?.id}`, {
        credentials: 'include' // Include cookies in the request
      });
      if (!response.ok) throw new Error('Failed to fetch stats');
      const data = await response.json();
      setStats(data);
    } catch (error) {
      console.error('Error fetching help stats:', error);
      toast.error('Failed to load statistics');
    }
  };

  const fetchTickets = async () => {
    try {
      setTicketsLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20',
        userId: user?.id || '' // Use the actual admin user ID
      });

      if (statusFilter !== 'all') params.append('status', statusFilter);
      if (priorityFilter !== 'all') params.append('priority', priorityFilter);
      if (searchQuery) params.append('search', searchQuery);

      const response = await fetch(`/api/help/tickets?${params}`, {
        credentials: 'include' // Include cookies in the request
      });
      if (!response.ok) throw new Error('Failed to fetch tickets');

      const data = await response.json();
      setTickets(data.tickets);
      setTotalPages(data.pagination.pages);
    } catch (error) {
      console.error('Error fetching tickets:', error);
      toast.error('Failed to load tickets');
    } finally {
      setTicketsLoading(false);
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-vista-light/20 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-vista-light/20 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-vista-light/20 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-vista-light">Help Center Management</h1>
          <p className="text-vista-light/70">Manage support tickets and view analytics</p>
        </div>
        <Button className="bg-vista-blue hover:bg-vista-blue/90">
          <MessageSquare className="w-4 h-4 mr-2" />
          View All Tickets
        </Button>
      </div>

      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-vista-card border-vista-light/10">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-vista-light/70 text-sm">Total Tickets</p>
                  <p className="text-2xl font-bold text-vista-light">{stats.overview.totalTickets}</p>
                </div>
                <MessageSquare className="w-8 h-8 text-vista-blue" />
              </div>
              <div className="mt-2 text-sm text-vista-light/60">
                +{stats.overview.recentTickets} this month
              </div>
            </CardContent>
          </Card>

          <Card className="bg-vista-card border-vista-light/10">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-vista-light/70 text-sm">Open Tickets</p>
                  <p className="text-2xl font-bold text-vista-light">{stats.overview.openTickets}</p>
                </div>
                <AlertCircle className="w-8 h-8 text-orange-400" />
              </div>
              <div className="mt-2 text-sm text-vista-light/60">
                {stats.overview.inProgressTickets} in progress
              </div>
            </CardContent>
          </Card>

          <Card className="bg-vista-card border-vista-light/10">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-vista-light/70 text-sm">Avg Resolution</p>
                  <p className="text-2xl font-bold text-vista-light">{stats.overview.averageResolutionTime}h</p>
                </div>
                <Clock className="w-8 h-8 text-yellow-400" />
              </div>
              <div className="mt-2 text-sm text-vista-light/60">
                {stats.overview.recentResolved} resolved this month
              </div>
            </CardContent>
          </Card>

          <Card className="bg-vista-card border-vista-light/10">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-vista-light/70 text-sm">Satisfaction</p>
                  <p className="text-2xl font-bold text-vista-light">{stats.satisfaction.averageRating}/5</p>
                </div>
                <Star className="w-8 h-8 text-yellow-400" />
              </div>
              <div className="mt-2 text-sm text-vista-light/60">
                {stats.satisfaction.totalRatings} ratings
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs defaultValue="tickets" className="space-y-6">
        <TabsList className="bg-vista-card border-vista-light/10">
          <TabsTrigger value="tickets" className="data-[state=active]:bg-vista-blue data-[state=active]:text-white">
            Tickets
          </TabsTrigger>
          <TabsTrigger value="analytics" className="data-[state=active]:bg-vista-blue data-[state=active]:text-white">
            Analytics
          </TabsTrigger>
          <TabsTrigger value="categories" className="data-[state=active]:bg-vista-blue data-[state=active]:text-white">
            Categories
          </TabsTrigger>
        </TabsList>

        <TabsContent value="tickets" className="space-y-6">
          {/* Filters */}
          <Card className="bg-vista-card border-vista-light/10">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-vista-light/60 w-4 h-4" />
                  <Input
                    placeholder="Search tickets..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 bg-vista-dark border-vista-light/20 text-vista-light"
                  />
                </div>
                
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="bg-vista-dark border-vista-light/20 text-vista-light">
                    <SelectValue placeholder="All Statuses" />
                  </SelectTrigger>
                  <SelectContent className="bg-vista-dark border-vista-light/20">
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="open">Open</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="waiting_for_user">Waiting for User</SelectItem>
                    <SelectItem value="resolved">Resolved</SelectItem>
                    <SelectItem value="closed">Closed</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                  <SelectTrigger className="bg-vista-dark border-vista-light/20 text-vista-light">
                    <SelectValue placeholder="All Priorities" />
                  </SelectTrigger>
                  <SelectContent className="bg-vista-dark border-vista-light/20">
                    <SelectItem value="all">All Priorities</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>

                <Button 
                  variant="outline" 
                  onClick={() => {
                    setSearchQuery('');
                    setStatusFilter('all');
                    setPriorityFilter('all');
                  }}
                  className="border-vista-light/20 text-vista-light hover:bg-vista-light/10"
                >
                  <Filter className="w-4 h-4 mr-2" />
                  Clear
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Tickets List */}
          <Card className="bg-vista-card border-vista-light/10">
            <CardHeader>
              <CardTitle className="text-vista-light">Support Tickets</CardTitle>
            </CardHeader>
            <CardContent>
              {ticketsLoading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="h-4 bg-vista-light/20 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-vista-light/20 rounded w-1/2"></div>
                    </div>
                  ))}
                </div>
              ) : tickets.length > 0 ? (
                <div className="space-y-4">
                  {tickets.map((ticket) => {
                    const StatusIcon = statusConfig[ticket.status].icon;
                    return (
                      <Link key={ticket._id} href={`/help/tickets/${ticket._id}`}>
                        <div className="p-4 border border-vista-light/10 rounded-lg hover:border-vista-blue/30 transition-colors cursor-pointer">
                          <div className="flex items-start justify-between gap-4">
                            <div className="flex-1">
                              <div className="flex items-center gap-3 mb-2">
                                <h3 className="font-semibold text-vista-light hover:text-vista-blue transition-colors">
                                  {ticket.subject}
                                </h3>
                                <Badge variant="outline" className="border-vista-light/20 text-vista-light/70 text-xs">
                                  #{ticket.ticketNumber}
                                </Badge>
                                {ticket.escalated && (
                                  <Badge className="bg-red-500/20 text-red-400 text-xs">
                                    Escalated
                                  </Badge>
                                )}
                              </div>
                              
                              <div className="flex flex-wrap items-center gap-2 mb-2">
                                <Badge className={statusConfig[ticket.status].color}>
                                  <StatusIcon className="w-3 h-3 mr-1" />
                                  {statusConfig[ticket.status].label}
                                </Badge>
                                <Badge className={priorityConfig[ticket.priority].color}>
                                  {priorityConfig[ticket.priority].label}
                                </Badge>
                                <Badge variant="outline" className="border-vista-light/20 text-vista-light/60 text-xs">
                                  {ticket.category.replace('_', ' ')}
                                </Badge>
                              </div>

                              <div className="flex items-center gap-4 text-sm text-vista-light/60">
                                <span>{ticket.userId.name} ({ticket.userId.email})</span>
                                <span>•</span>
                                <span>{formatDate(ticket.createdAt)}</span>
                                {ticket.assignedTo && (
                                  <>
                                    <span>•</span>
                                    <span>Assigned to {ticket.assignedTo.name}</span>
                                  </>
                                )}
                                <span>•</span>
                                <span>{ticket.responses.length} responses</span>
                              </div>
                            </div>
                            
                            <Eye className="w-4 h-4 text-vista-light/60" />
                          </div>
                        </div>
                      </Link>
                    );
                  })}

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="flex justify-center gap-2 mt-6">
                      <Button
                        variant="outline"
                        onClick={() => setPage(p => Math.max(1, p - 1))}
                        disabled={page === 1}
                        className="border-vista-light/20 text-vista-light"
                      >
                        Previous
                      </Button>
                      <span className="flex items-center px-4 text-vista-light/70">
                        Page {page} of {totalPages}
                      </span>
                      <Button
                        variant="outline"
                        onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                        disabled={page === totalPages}
                        className="border-vista-light/20 text-vista-light"
                      >
                        Next
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <MessageSquare className="w-12 h-12 text-vista-light/40 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-vista-light mb-2">No tickets found</h3>
                  <p className="text-vista-light/70">
                    {searchQuery || statusFilter !== 'all' || priorityFilter !== 'all' 
                      ? 'Try adjusting your filters to see more results.'
                      : 'No support tickets have been created yet.'
                    }
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {stats && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Category Breakdown */}
              <Card className="bg-vista-card border-vista-light/10">
                <CardHeader>
                  <CardTitle className="text-vista-light flex items-center gap-2">
                    <BarChart3 className="w-5 h-5" />
                    Tickets by Category
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {stats.ticketsByCategory.map((category) => (
                      <div key={category._id} className="flex items-center justify-between">
                        <span className="text-vista-light capitalize">{category._id.replace('_', ' ')}</span>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="border-vista-light/20 text-vista-light/70">
                            {category.count} total
                          </Badge>
                          <Badge className="bg-green-500/20 text-green-400">
                            {category.resolved} resolved
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Top Agents */}
              <Card className="bg-vista-card border-vista-light/10">
                <CardHeader>
                  <CardTitle className="text-vista-light flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    Top Support Agents
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {stats.topAgents.slice(0, 5).map((agent) => (
                      <div key={agent._id} className="flex items-center justify-between">
                        <span className="text-vista-light">{agent.assignedToName}</span>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="border-vista-light/20 text-vista-light/70">
                            {agent.totalTickets} assigned
                          </Badge>
                          <Badge className="bg-green-500/20 text-green-400">
                            {agent.resolvedTickets} resolved
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="categories">
          <Card className="bg-vista-card border-vista-light/10">
            <CardHeader>
              <CardTitle className="text-vista-light">Manage Categories</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-vista-light/70">Category management coming soon...</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
