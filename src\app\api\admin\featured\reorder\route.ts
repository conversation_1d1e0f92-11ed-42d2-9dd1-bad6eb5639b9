import { NextRequest, NextResponse } from 'next/server';

/**
 * POST /api/admin/featured/reorder
 * Reorder featured content
 */
export async function POST(request: NextRequest) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the FeaturedContent schema directly
    const FeaturedContentSchema = new mongoose.default.Schema({
      contentId: mongoose.default.Schema.Types.ObjectId,
      contentType: String,
      title: String,
      posterPath: String,
      order: Number,
      active: Boolean
    }, {
      timestamps: true
    });

    // Get the FeaturedContent model
    const FeaturedContent = mongoose.default.models.FeaturedContent ||
                           mongoose.default.model('FeaturedContent', FeaturedContentSchema);

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Get request data
    const data = await request.json();

    // Validate required fields
    if (!data.items || !Array.isArray(data.items)) {
      return NextResponse.json({ error: 'Items array is required' }, { status: 400 });
    }

    // Update order for each item
    const updatePromises = data.items.map(async (item: any, index: number) => {
      if (!item.id) {
        throw new Error('Each item must have an id');
      }

      return FeaturedContent.findByIdAndUpdate(item.id, { order: index + 1 });
    });

    // Wait for all updates to complete
    await Promise.all(updatePromises);

    // Log admin activity directly
    await UserActivity.create({
      userId: new mongoose.default.Types.ObjectId(userId),
      type: 'admin',
      action: 'reorder_featured_content',
      details: `Admin reordered featured content`,
      ipAddress: request.ip || request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      timestamp: new Date()
    });

    // Return success
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error reordering featured content:', error);
    return NextResponse.json(
      { error: 'Failed to reorder featured content', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
