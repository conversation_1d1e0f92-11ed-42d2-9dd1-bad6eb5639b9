'use client';

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Eye,
  UserCheck,
  BarChart3,
  Globe,
  Smartphone,
  Laptop,
  AreaChart,
  BarChart2,
  Per<PERSON>,
  <PERSON><PERSON>hart,
  AlertCircle,
  TrendingUp,
  TrendingDown,
  Clock,
  MousePointerClick,
  ArrowUpRight,
  ArrowDownRight,
  Activity,
  UserMinus,
  Timer
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { VisitorStats as VisitorStatsInterface } from '@/hooks/useVisitorData';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import type { VisitorStats } from '@/hooks/useVisitorData';

interface VisitorStatsProps {
  stats?: VisitorStats;
  isLoading: boolean;
}

export default function VisitorStats({ stats, isLoading }: VisitorStatsProps) {
  // Format numbers with commas
  const formatNumber = (num: number | undefined) => {
    if (num === undefined || isNaN(num)) return '0';
    return new Intl.NumberFormat('en-US', {
      notation: num > 9999 ? "compact" : "standard",
      compactDisplay: "short"
    }).format(num);
  };

  // Format percentages
  const formatPercent = (num: number | undefined) => {
    if (num === undefined || isNaN(num)) return '0.0%';
    return `${(num * 100).toFixed(1)}%`;
  };

  // Format time in minutes and seconds
  const formatTime = (seconds: number | undefined) => {
    if (seconds === undefined || isNaN(seconds) || seconds < 0) return '0m 0s';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}m ${secs}s`;
  };
  
  // Helper to safely access stat values with fallbacks
  const getStat = (key: keyof VisitorStats) => {
    if (!stats) return undefined;
    
    // Explicitly handle the numeric properties we know about
    switch(key) {
      case 'totalVisitors':
      case 'totalVisits':
      case 'totalPagesViewed':
      case 'convertedCount':
      case 'averageVisits':
      case 'averagePagesViewed':
      case 'percentNewVisitors':
      case 'bounceRate':
      case 'averageSessionTime':
        return stats[key] as number;
      default:
        // For unknown properties or growth, return undefined
        return undefined;
    }
  };
  
  // Helper to get growth data with fallbacks
  const getGrowth = (key: 'visitors' | 'visits' | 'pagesViewed' | 'signups') => {
    if (!stats?.growth) return undefined;
    return stats.growth[key];
  };
  
  // Calculate growth percentages safely
  const calculateGrowthPercent = (current: number | undefined, growth: number | undefined) => {
    if (current === undefined || growth === undefined || current === 0) return 0;
    return growth / current;
  };

  if (isLoading) {
    return (
      <div className="grid gap-4 grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className="shadow-sm">
            <CardHeader className="pb-2">
              <div className="h-5 w-20 bg-muted animate-pulse rounded"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 w-16 bg-muted animate-pulse rounded mb-2"></div>
              <div className="h-5 w-24 bg-muted/60 animate-pulse rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Helper to calculate growth percentage with proper typing
  const renderGrowthIndicator = (statKey: keyof VisitorStats, growthKey: 'visitors' | 'visits' | 'pagesViewed' | 'signups') => {
    const statValue = getStat(statKey) as number | undefined;
    const growthValue = getGrowth(growthKey);
    
    if (!growthValue) return null;
    
    const percentage = Math.abs(calculateGrowthPercent(statValue, growthValue));
    const isPositive = growthValue > 0;
    
    return (
      <>
        {isPositive ? (
          <>
            <TrendingUp className="h-3 w-3 text-green-500" />
            <span className="text-green-600 font-medium">
              {formatPercent(percentage)}
            </span>
          </>
        ) : (
          <>
            <TrendingDown className="h-3 w-3 text-red-500" />
            <span className="text-red-600 font-medium">
              {formatPercent(percentage)}
            </span>
          </>
        )}
      </>
    );
  };

  return (
    <div className="grid gap-4 grid-cols-2 lg:grid-cols-4">
      <Card className="shadow-sm">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium flex items-center gap-1.5">
            <Users className="h-4 w-4 text-blue-500" />
            Visitors
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(getStat('totalVisitors'))}</div>
          <p className="text-xs text-muted-foreground mt-1">
            <span className="flex items-center gap-1.5">
              {renderGrowthIndicator('totalVisitors', 'visitors')}
              <span>from previous period</span>
            </span>
          </p>
        </CardContent>
      </Card>

      <Card className="shadow-sm">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium flex items-center gap-1.5">
            <UserMinus className="h-4 w-4 text-teal-500" />
            Bounce Rate
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatPercent(getStat('bounceRate'))}</div>
          <p className="text-xs text-muted-foreground mt-1">
            Visitors who leave after viewing one page
          </p>
        </CardContent>
      </Card>

      <Card className="shadow-sm">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium flex items-center gap-1.5">
            <Timer className="h-4 w-4 text-purple-500" />
            Avg. Session Time
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatTime(getStat('averageSessionTime'))}</div>
          <p className="text-xs text-muted-foreground mt-1">
            Average time spent on your site
          </p>
        </CardContent>
      </Card>

      <Card className="shadow-sm">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium flex items-center gap-1.5">
            <UserCheck className="h-4 w-4 text-indigo-500" />
            Converted Users
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(getStat('convertedCount'))}</div>
          <p className="text-xs text-muted-foreground mt-1">
            <span className="flex items-center gap-1.5">
              {renderGrowthIndicator('convertedCount', 'signups')}
              <span>sign-up conversion rate</span>
            </span>
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
