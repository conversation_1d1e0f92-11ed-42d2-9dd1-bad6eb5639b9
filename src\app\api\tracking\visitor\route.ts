import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import { UAParser } from 'ua-parser-js';
import { v4 as uuidv4 } from 'uuid';
import { generateNickname } from '@/lib/nickname-generator';
import { getLocationFromIp } from '@/lib/geo-utils';
import { createHash } from 'crypto';

/**
 * POST /api/tracking/visitor
 * Track anonymous visitor data
 *
 * This endpoint handles:
 * 1. Creating new visitor records for first-time visitors
 * 2. Updating existing visitor records for returning visitors
 * 3. Tracking essential visitor information
 * 4. Generating a fingerprint hash to prevent duplicates
 */
export async function POST(request: NextRequest) {
  try {
    // Get request data
    const data = await request.json();
    const { referrer, browserInfo: clientBrowserInfo = {} } = data;

    // Get visitor ID from cookie or create a new one
    let visitorId = request.cookies.get('visitorId')?.value;
    let isNewCookie = false;

    // If no visitor ID, create a new one
    if (!visitorId) {
      visitorId = uuidv4();
      isNewCookie = true;
    }

    // Get IP address and user agent
    const ipAddress = request.headers.get('x-forwarded-for')?.split(',')[0] ||
                      request.headers.get('x-real-ip') ||
                      'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Parse user agent to get device, browser, and OS info
    const parser = new UAParser(userAgent);
    const browserInfo = parser.getBrowser();
    const osInfo = parser.getOS();
    const deviceInfo = parser.getDevice();

    // Determine device type
    let deviceType = 'Desktop';
    if (deviceInfo.type === 'mobile' || deviceInfo.type === 'tablet') {
      deviceType = deviceInfo.type === 'mobile' ? 'Mobile' : 'Tablet';
    }

    // Generate a fingerprint hash to identify unique visitors more accurately
    // Include client-provided browser information (screen resolution, timezone, etc.)
    const fingerprint = createVisitorFingerprint({
      ipAddress,
      userAgent,
      browser: browserInfo.name,
      os: osInfo.name,
      device: deviceType,
      // Include client-side collected data if available
      screenWidth: clientBrowserInfo.screenWidth,
      screenHeight: clientBrowserInfo.screenHeight,
      colorDepth: clientBrowserInfo.colorDepth,
      timezone: clientBrowserInfo.timezone,
      language: clientBrowserInfo.language,
      platform: clientBrowserInfo.platform
      // No salt - we want the same fingerprint for the same device/browser
    });

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Import the AnonymousVisitor model
    const AnonymousVisitor = (await import('@/models/AnonymousVisitor')).default;

    // First check if visitor exists by visitorId (cookie) - most reliable method
    let existingVisitor = await AnonymousVisitor.findOne({ visitorId });
    let matchMethod = existingVisitor ? 'cookie' : null;

    // If no visitor by cookie ID, use a multi-factor approach to identify the visitor
    if (!existingVisitor) {
      // Create an array of potential queries to find the visitor, ordered by reliability
      const potentialQueries = [];

      // 1. Check by fingerprint (if available) - most reliable method
      if (fingerprint) {
        // For mobile devices with special fingerprint format (deviceHash:mainHash)
        if (deviceType === 'Mobile' && fingerprint.includes(':')) {
          const [deviceHash, mainHash] = fingerprint.split(':');

          // Try exact fingerprint match first
          potentialQueries.push({
            query: { fingerprint },
            method: 'fingerprint-exact',
            reliability: 0.95 // 95% confidence
          });

          // Then try matching just the device hash part (for mobile devices that changed networks)
          potentialQueries.push({
            query: {
              fingerprint: { $regex: `^${deviceHash}:` },
              device: 'Mobile'
            },
            method: 'fingerprint-device-mobile',
            reliability: 0.85 // 85% confidence
          });

          // Also try matching the main hash part (for cases where the format changed)
          potentialQueries.push({
            query: {
              fingerprint: { $regex: `:${mainHash}$` }
            },
            method: 'fingerprint-main',
            reliability: 0.8 // 80% confidence
          });
        } else {
          // For non-mobile or old format fingerprints, just do an exact match
          potentialQueries.push({
            query: { fingerprint },
            method: 'fingerprint',
            reliability: 0.95 // 95% confidence
          });
        }
      }

      // 2. Check by hardware-specific identifiers for mobile
      if (deviceType === 'Mobile' &&
          clientBrowserInfo.pixelRatio &&
          clientBrowserInfo.cpuCores &&
          clientBrowserInfo.memory) {
        potentialQueries.push({
          query: {
            device: 'Mobile',
            'metadata.pixelRatio': clientBrowserInfo.pixelRatio,
            'metadata.cpuCores': clientBrowserInfo.cpuCores,
            'metadata.memory': clientBrowserInfo.memory
          },
          method: 'mobile-hardware',
          reliability: 0.9 // 90% confidence
        });
      }

      // 3. Check by IP address + browser + OS + device (very specific combination)
      if (ipAddress && ipAddress !== 'unknown' && browserInfo.name && osInfo.name && deviceType) {
        potentialQueries.push({
          query: {
            ipAddress,
            browser: browserInfo.name,
            os: osInfo.name,
            device: deviceType
          },
          method: 'ip-browser-os-device',
          reliability: 0.85 // 85% confidence
        });
      }

      // 4. For mobile: check by device characteristics without IP
      // This helps identify the same mobile device across different networks
      if (deviceType === 'Mobile' &&
          clientBrowserInfo.screenWidth &&
          clientBrowserInfo.screenHeight &&
          clientBrowserInfo.pixelRatio) {
        potentialQueries.push({
          query: {
            device: 'Mobile',
            'metadata.screenWidth': clientBrowserInfo.screenWidth,
            'metadata.screenHeight': clientBrowserInfo.screenHeight,
            'metadata.pixelRatio': clientBrowserInfo.pixelRatio,
            browser: browserInfo.name,
            os: osInfo.name
          },
          method: 'mobile-screen-browser-os',
          reliability: 0.8 // 80% confidence
        });
      }

      // 5. Check by IP address + same device
      if (ipAddress && ipAddress !== 'unknown' && deviceType) {
        // For mobile, we need to be more careful with IP-based matching
        // as mobile devices change IPs frequently
        const reliability = deviceType === 'Mobile' ? 0.6 : 0.7;

        potentialQueries.push({
          query: {
            ipAddress,
            device: deviceType
          },
          method: 'ip-device',
          reliability // 70% for desktop, 60% for mobile
        });
      }

      // 6. Check by IP address + browser + OS (useful for same location, same device type)
      if (ipAddress && ipAddress !== 'unknown' && browserInfo.name && osInfo.name) {
        potentialQueries.push({
          query: {
            ipAddress,
            browser: browserInfo.name,
            os: osInfo.name
          },
          method: 'ip-browser-os',
          reliability: 0.6 // 60% confidence
        });
      }

      // 7. Try IP address + screen dimensions (can be reliable for desktop)
      if (ipAddress && ipAddress !== 'unknown' &&
          clientBrowserInfo.screenWidth && clientBrowserInfo.screenHeight) {
        potentialQueries.push({
          query: {
            ipAddress,
            'metadata.screenWidth': clientBrowserInfo.screenWidth,
            'metadata.screenHeight': clientBrowserInfo.screenHeight
          },
          method: 'ip-screen',
          reliability: 0.5 // 50% confidence
        });
      }

      // 8. Try IP address alone as last resort (only for non-mobile)
      if (ipAddress && ipAddress !== 'unknown' && deviceType !== 'Mobile') {
        potentialQueries.push({
          query: { ipAddress },
          method: 'ip-only',
          reliability: 0.3 // 30% confidence - least reliable
        });
      }

      // Try each query in order until we find a visitor
      for (const { query, method, reliability } of potentialQueries) {
        if (!existingVisitor) {
          // Find the most recently active visitor matching this query
          existingVisitor = await AnonymousVisitor.findOne(query).sort({ lastVisit: -1 });

          if (existingVisitor) {
            console.log(`Found visitor by ${method}: ${JSON.stringify(query)}`);
            matchMethod = method;

            // For less reliable methods, double check the match isn't too old
            // (to avoid false positives with recycled IPs)
            if (reliability < 0.7) {
              const lastVisitTime = new Date(existingVisitor.lastVisit).getTime();
              const now = Date.now();
              const daysSinceLastVisit = (now - lastVisitTime) / (1000 * 60 * 60 * 24);

              // If the last visit was more than 7 days ago for a low-reliability match,
              // treat this as a new visitor instead
              if (daysSinceLastVisit > 7) {
                console.log(`Rejecting match: Last visit was ${daysSinceLastVisit.toFixed(1)} days ago, reliability too low`);
                existingVisitor = null;
                matchMethod = null;
              }
            }
          }
        }
      }

      // If we found a visitor, update their ID if it doesn't match
      if (existingVisitor && existingVisitor.visitorId !== visitorId) {
        // Store the old ID for reference
        const oldVisitorId = existingVisitor.visitorId;

        // Track previous IDs
        const previousIds = existingVisitor.previousVisitorIds || [];
        if (!previousIds.includes(oldVisitorId)) {
          previousIds.push(oldVisitorId);
        }

        // Update to the new ID, fingerprint, and userAgent (to keep visitor data fresh)
        existingVisitor.visitorId = visitorId;
        existingVisitor.fingerprint = fingerprint || existingVisitor.fingerprint;
        existingVisitor.userAgent = userAgent;
        existingVisitor.previousVisitorIds = previousIds;

        // Mark as deduplicated
        existingVisitor.isDeduplicated = true;
        existingVisitor.deduplicatedAt = new Date();
        existingVisitor.deduplicatedMethod = matchMethod;

        await existingVisitor.save();

        console.log(`Updated visitor ID from ${oldVisitorId} to ${visitorId} based on ${matchMethod} match`);
      }
    }

    let isNewVisitor = false;

    if (existingVisitor) {
      // Update existing visitor - keep track of client information as it can change between visits
      await AnonymousVisitor.updateOne(
        { visitorId },
        {
          $set: {
            lastVisit: new Date(),
            fingerprint: fingerprint || existingVisitor.fingerprint,
            // Update device information in case user upgrades browser or OS
            userAgent: userAgent || existingVisitor.userAgent,
            browser: browserInfo.name || existingVisitor.browser,
            os: osInfo.name || existingVisitor.os,
            device: deviceType || existingVisitor.device,
            // Update metadata with latest client information
            'metadata.pixelRatio': clientBrowserInfo.pixelRatio,
            'metadata.cpuCores': clientBrowserInfo.cpuCores,
            'metadata.connectionType': clientBrowserInfo.connectionType,
            'metadata.effectiveType': clientBrowserInfo.effectiveType,
            'metadata.touchSupport': clientBrowserInfo.touchSupport,
            'metadata.isMobile': clientBrowserInfo.isMobile,
            'metadata.lastClientTimestamp': clientBrowserInfo.timestamp
          },
          $inc: { visitCount: 1 }
        }
      );

      console.log(`Updated existing visitor: ${visitorId} (IP: ${ipAddress}, Device: ${deviceType})`);
    } else {
      // Perform a more thorough check for potential duplicates before creating a new visitor
      // This helps catch edge cases that might have been missed in the initial identification

      // Define a type for our query conditions
      type QueryCondition = {
        fingerprint?: string | { $regex: string };
        ipAddress?: string;
        device?: string;
        browser?: string;
        os?: string;
        'metadata.screenWidth'?: number;
        'metadata.screenHeight'?: number;
        'metadata.pixelRatio'?: number;
        'metadata.cpuCores'?: number;
        'metadata.memory'?: number;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        [key: string]: any; // Allow for dynamic properties in MongoDB queries
      };

      // Build a comprehensive query to find any potential matches
      const potentialDuplicateQuery: { $or: QueryCondition[] } = {
        $or: []
      };

      // Add fingerprint check if available
      if (fingerprint) {
        // For mobile devices with special fingerprint format (deviceHash:mainHash)
        if (deviceType === 'Mobile' && fingerprint.includes(':')) {
          const [deviceHash] = fingerprint.split(':');

          // Try exact fingerprint match
          potentialDuplicateQuery.$or.push({ fingerprint });

          // Try matching just the device hash part (for mobile devices that changed networks)
          potentialDuplicateQuery.$or.push({
            fingerprint: { $regex: `^${deviceHash}:` },
            device: 'Mobile'
          });
        } else {
          // For non-mobile or old format fingerprints
          potentialDuplicateQuery.$or.push({ fingerprint });
        }
      }

      // Add hardware-specific identifiers for mobile
      if (deviceType === 'Mobile' &&
          clientBrowserInfo.pixelRatio &&
          clientBrowserInfo.cpuCores) {
        potentialDuplicateQuery.$or.push({
          device: 'Mobile',
          'metadata.pixelRatio': clientBrowserInfo.pixelRatio,
          'metadata.cpuCores': clientBrowserInfo.cpuCores
        });
      }

      // Add device characteristics without IP for mobile
      if (deviceType === 'Mobile' &&
          clientBrowserInfo.screenWidth &&
          clientBrowserInfo.screenHeight &&
          clientBrowserInfo.pixelRatio) {
        potentialDuplicateQuery.$or.push({
          device: 'Mobile',
          'metadata.screenWidth': clientBrowserInfo.screenWidth,
          'metadata.screenHeight': clientBrowserInfo.screenHeight,
          'metadata.pixelRatio': clientBrowserInfo.pixelRatio,
          browser: browserInfo.name,
          os: osInfo.name
        });
      }

      // Add IP + device check (more reliable for desktop than mobile)
      if (ipAddress && ipAddress !== 'unknown' && deviceType) {
        potentialDuplicateQuery.$or.push({
          ipAddress,
          device: deviceType
        });
      }

      // Add IP + browser + OS check
      if (ipAddress && ipAddress !== 'unknown' && browserInfo.name && osInfo.name) {
        potentialDuplicateQuery.$or.push({
          ipAddress,
          browser: browserInfo.name,
          os: osInfo.name
        });
      }

      // Add screen dimensions check for desktop
      if (ipAddress && ipAddress !== 'unknown' &&
          clientBrowserInfo.screenWidth && clientBrowserInfo.screenHeight &&
          deviceType === 'Desktop') {
        potentialDuplicateQuery.$or.push({
          ipAddress,
          'metadata.screenWidth': clientBrowserInfo.screenWidth,
          'metadata.screenHeight': clientBrowserInfo.screenHeight
        });
      }

      // Only run this check if we have at least one condition
      let potentialDuplicate = null;
      if (potentialDuplicateQuery.$or.length > 0) {
        // Find the most recently active potential duplicate
        potentialDuplicate = await AnonymousVisitor.findOne(potentialDuplicateQuery)
          .sort({ lastVisit: -1 });

        if (potentialDuplicate) {
          // Check if this is a recent visitor (within last 30 days)
          const lastVisitTime = new Date(potentialDuplicate.lastVisit).getTime();
          const now = Date.now();
          const daysSinceLastVisit = (now - lastVisitTime) / (1000 * 60 * 60 * 24);

          if (daysSinceLastVisit <= 30) {
            // This is likely the same visitor with a new cookie
            console.log(`Found potential duplicate: ${potentialDuplicate.visitorId} (last active ${daysSinceLastVisit.toFixed(1)} days ago)`);

            // Store the old ID
            const oldVisitorId = potentialDuplicate.visitorId;

            // Track previous IDs
            const previousIds = potentialDuplicate.previousVisitorIds || [];
            if (!previousIds.includes(oldVisitorId)) {
              previousIds.push(oldVisitorId);
            }

            // Update the visitor with the new ID and information
            potentialDuplicate.visitorId = visitorId;
            potentialDuplicate.fingerprint = fingerprint;
            potentialDuplicate.userAgent = userAgent;
            potentialDuplicate.browser = browserInfo.name;
            potentialDuplicate.os = osInfo.name;
            potentialDuplicate.device = deviceType;
            potentialDuplicate.lastVisit = new Date();
            potentialDuplicate.previousVisitorIds = previousIds;
            potentialDuplicate.visitCount += 1;

            // Mark as deduplicated
            potentialDuplicate.isDeduplicated = true;
            potentialDuplicate.deduplicatedAt = new Date();
            potentialDuplicate.deduplicatedMethod = 'pre-creation-check';

            await potentialDuplicate.save();

            console.log(`Updated potential duplicate: ${oldVisitorId} -> ${visitorId}`);

            // Use this as our existing visitor
            existingVisitor = potentialDuplicate;
            return;
          }
        }
      }

      // If we reach here, we need to create a new visitor
      // Log any similar visitors for debugging
      const similarVisitorsCount = await AnonymousVisitor.countDocuments({
        ipAddress,
        device: deviceType
      });

      if (similarVisitorsCount > 0) {
        console.log(`Creating new visitor with ${similarVisitorsCount} similar visitors with same IP+device`);
      }

      // Generate a friendly nickname for the visitor
      const nickname = generateNickname(visitorId);

      // Get location information from IP address
      const locationInfo = await getLocationFromIp(ipAddress);

      // Create new visitor with comprehensive information
      const newVisitor = new AnonymousVisitor({
        visitorId,
        nickname,
        ipAddress,
        userAgent,
        fingerprint,
        firstVisit: new Date(),
        lastVisit: new Date(),
        visitCount: 1,
        pagesViewed: 0,
        referrer: referrer || 'Direct',
        browser: browserInfo.name,
        os: osInfo.name,
        device: deviceType,
        country: locationInfo.country,
        countryCode: locationInfo.countryCode,
        region: locationInfo.region,
        city: locationInfo.city,
        timezone: locationInfo.timezone,
        latitude: locationInfo.latitude,
        longitude: locationInfo.longitude,
        convertedToUser: false,
        metadata: {
          // Store additional identification details
          screenWidth: clientBrowserInfo.screenWidth,
          screenHeight: clientBrowserInfo.screenHeight,
          colorDepth: clientBrowserInfo.colorDepth,
          pixelRatio: clientBrowserInfo.pixelRatio,
          orientation: clientBrowserInfo.orientation,
          timezone: clientBrowserInfo.timezone,
          language: clientBrowserInfo.language,
          platform: clientBrowserInfo.platform,
          memory: clientBrowserInfo.memory,
          cpuCores: clientBrowserInfo.cpuCores,
          connectionType: clientBrowserInfo.connectionType,
          effectiveType: clientBrowserInfo.effectiveType,
          touchSupport: clientBrowserInfo.touchSupport,
          touchPoints: clientBrowserInfo.touchPoints,
          cookiesEnabled: clientBrowserInfo.cookiesEnabled,
          doNotTrack: clientBrowserInfo.doNotTrack,
          isMobile: clientBrowserInfo.isMobile,
          vendor: clientBrowserInfo.vendor,
          pdfViewerEnabled: clientBrowserInfo.pdfViewerEnabled,
          creationDate: new Date(),
          clientTimestamp: clientBrowserInfo.timestamp
        }
      });

      // Save new visitor
      await newVisitor.save();
      isNewVisitor = true;

      console.log(`Created new visitor: ${visitorId} (IP: ${ipAddress}, Device: ${deviceType}, Location: ${locationInfo.city || 'Unknown'}, ${locationInfo.country || 'Unknown'})`);
    }

    // Create the response
    const response = NextResponse.json({
      success: true,
      isNewVisitor,
      visitorId
    });

    // Set cookie if it's a new visitor
    if (isNewCookie) {
      // Set cookie with visitor ID (expires in 1 year)
      const oneYear = 60 * 60 * 24 * 365;
      response.cookies.set('visitorId', visitorId, {
        maxAge: oneYear,
        path: '/',
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production',
        httpOnly: true
      });
    }

    return response;
  } catch (error) {
    console.error('Error tracking visitor:', error);
    return NextResponse.json(
      { error: 'Failed to track visitor', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/tracking/visitor
 * Get current visitor ID (for client-side use)
 */
export async function GET(request: NextRequest) {
  try {
    // Get visitor ID from cookie
    const visitorId = request.cookies.get('visitorId')?.value;

    return NextResponse.json({
      visitorId: visitorId || null
    });
  } catch (error) {
    console.error('Error getting visitor ID:', error);
    return NextResponse.json(
      { error: 'Failed to get visitor ID', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * Helper function to create a fingerprint hash for a visitor
 * This helps identify the same visitor even if they clear cookies
 */
interface VisitorFingerprintData {
  ipAddress: string;
  userAgent: string;
  browser?: string;
  os?: string;
  device?: string;
  screenWidth?: number;
  screenHeight?: number;
  colorDepth?: number;
  timezone?: string;
  language?: string;
  platform?: string;
  salt?: string;
  [key: string]: string | number | undefined;
}

function createVisitorFingerprint(data: VisitorFingerprintData): string {
  try {
    // Create a copy of the data without the salt
    // This ensures the fingerprint is consistent across visits
    const fingerprintData = { ...data };
    delete fingerprintData.salt; // Remove the salt to avoid unique fingerprints for the same visitor

    // Create multiple fingerprint components with different weights
    // This allows for more accurate matching even if some data changes

    // 1. Device fingerprint - highest reliability
    // Focus on hardware characteristics that rarely change
    const deviceKeys = ['device', 'screenWidth', 'screenHeight', 'colorDepth', 'platform'];
    const deviceValues = deviceKeys
      .filter(key => fingerprintData[key] !== undefined && fingerprintData[key] !== null)
      .map(key => `${key}:${fingerprintData[key]}`)
      .join('|');

    // 2. Software fingerprint - medium reliability
    // Browser and OS can be updated but usually remain consistent
    const softwareKeys = ['browser', 'os', 'language', 'timezone'];
    const softwareValues = softwareKeys
      .filter(key => fingerprintData[key] !== undefined && fingerprintData[key] !== null)
      .map(key => `${key}:${fingerprintData[key]}`)
      .join('|');

    // 3. Network fingerprint - lower reliability but useful
    // IP address can change but is still valuable for identification
    const networkValues = fingerprintData.ipAddress ? `ipAddress:${fingerprintData.ipAddress}` : '';

    // Create separate fingerprints for different scenarios
    // This helps us match visitors even if some data changes

    // Secondary fingerprint - with IP (useful for same-session identification)
    const secondaryFingerprint = [
      deviceValues.length > 0 ? deviceValues : '',
      softwareValues.length > 0 ? softwareValues : '',
      networkValues.length > 0 ? networkValues : ''
    ].filter(Boolean).join('||');

    // Device-only fingerprint - for mobile devices that might change IPs frequently
    const deviceOnlyFingerprint = deviceValues.length > 0 ? deviceValues : '';

    // If we don't have enough data for a reliable fingerprint, return empty
    if (secondaryFingerprint.length < 10) {
      return '';
    }

    // Create a SHA-256 hash of the secondary fingerprint (with IP)
    // This is our main fingerprint that includes all data
    const mainHash = createHash('sha256')
      .update(secondaryFingerprint)
      .digest('hex');

    // For mobile devices, we also store a device-only hash in the first 8 characters
    // This helps identify the same mobile device across different networks
    if (fingerprintData.device === 'Mobile' && deviceOnlyFingerprint.length > 0) {
      const deviceHash = createHash('sha256')
        .update(deviceOnlyFingerprint)
        .digest('hex')
        .substring(0, 8);

      // Return a combined fingerprint with device hash prefix for mobile
      return `${deviceHash}:${mainHash}`;
    }

    return mainHash;
  } catch (error) {
    console.error('Error creating visitor fingerprint:', error);
    return '';
  }
}
