'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import {
  <PERSON><PERSON><PERSON> as Re<PERSON><PERSON>B<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON>rtsPieChart,
  Area,
  AreaChart,
  Bar,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell,
  Pie
} from 'recharts';
import { chartStyles, chartConfigs, getChartColors } from '@/lib/chart-theme';

// Define a generic chart data type
interface ChartDataItem {
  [key: string]: string | number;
}

// Common props for all chart types
interface ChartProps {
  data: ChartDataItem[];
  index: string;
  className?: string;
  showAnimation?: boolean;
  showLegend?: boolean;
  showGridLines?: boolean;
  valueFormatter?: (value: number) => string;
}

// Bar chart specific props
interface BarChartProps extends ChartProps {
  categories: string[];
  colors?: string[];
  layout?: 'vertical' | 'horizontal';
}

// Line chart specific props
interface LineChartProps extends ChartProps {
  categories: string[];
  colors?: string[];
  curveType?: 'linear' | 'smooth';
}

// Area chart specific props
interface AreaChartProps extends ChartProps {
  categories: string[];
  colors?: string[];
  curveType?: 'linear' | 'smooth';
  fillOpacity?: number;
}

// Pie chart specific props
interface PieChartProps {
  data: ChartDataItem[];
  index: string;
  category: string;
  colors?: string[];
  className?: string;
  showAnimation?: boolean;
  showLegend?: boolean;
  valueFormatter?: (value: number) => string;
  innerRadius?: number;
  outerRadius?: number;
}

// Enhanced chart implementations using Recharts

export function BarChart({
  data,
  index,
  categories,
  colors,
  className,
  showAnimation = true,
  showLegend = true,
  showGridLines = true,
  valueFormatter = (value) => `${value}`,
  layout = 'horizontal'
}: BarChartProps) {
  // Use theme colors if none provided
  const chartColors = colors || getChartColors(categories.length);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div style={chartStyles.tooltip.contentStyle}>
          <p className="text-vista-light font-medium mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {`${entry.dataKey}: ${valueFormatter(entry.value)}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className={cn("w-full h-full", className)}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsBarChart
          data={data}
          layout={layout === 'vertical' ? 'horizontal' : 'vertical'}
          margin={chartConfigs.barChart.margin}
        >
          {showGridLines && (
            <CartesianGrid
              strokeDasharray={chartStyles.grid.strokeDasharray}
              stroke={chartStyles.grid.stroke}
              opacity={chartStyles.grid.opacity}
              horizontal={chartStyles.grid.horizontal}
              vertical={chartStyles.grid.vertical}
            />
          )}
          <XAxis
            dataKey={layout === 'vertical' ? categories[0] : index}
            type={layout === 'vertical' ? 'number' : 'category'}
            tick={chartStyles.axis.tick}
            axisLine={chartStyles.axis.axisLine}
            tickLine={chartStyles.axis.tickLine}
          />
          <YAxis
            dataKey={layout === 'vertical' ? index : categories[0]}
            type={layout === 'vertical' ? 'category' : 'number'}
            tick={chartStyles.axis.tick}
            axisLine={chartStyles.axis.axisLine}
            tickLine={chartStyles.axis.tickLine}
            width={layout === 'vertical' ? 80 : undefined}
          />
          <Tooltip
            content={<CustomTooltip />}
            cursor={chartStyles.tooltip.cursor}
          />
          {showLegend && <Legend {...chartStyles.legend} />}
          {categories.map((category, i) => (
            <Bar
              key={category}
              dataKey={category}
              fill={chartColors[i % chartColors.length]}
              radius={chartConfigs.barChart.radius}
              animationDuration={showAnimation ? chartConfigs.barChart.animationDuration : 0}
              animationBegin={chartConfigs.barChart.animationBegin + (i * 100)}
            />
          ))}
        </RechartsBarChart>
      </ResponsiveContainer>
    </div>
  );
}

export function LineChart({
  data,
  index,
  categories,
  colors,
  className,
  showAnimation = true,
  showLegend = true,
  showGridLines = true,
  valueFormatter = (value) => `${value}`,
  curveType = 'linear'
}: LineChartProps) {
  // Use theme colors if none provided
  const chartColors = colors || getChartColors(categories.length);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div style={chartStyles.tooltip.contentStyle}>
          <p className="text-vista-light font-medium mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {`${entry.dataKey}: ${valueFormatter(entry.value)}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className={cn("w-full h-full", className)}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsLineChart
          data={data}
          margin={chartConfigs.lineChart.margin}
        >
          {showGridLines && (
            <CartesianGrid
              strokeDasharray={chartStyles.grid.strokeDasharray}
              stroke={chartStyles.grid.stroke}
              opacity={chartStyles.grid.opacity}
              horizontal={chartStyles.grid.horizontal}
              vertical={chartStyles.grid.vertical}
            />
          )}
          <XAxis
            dataKey={index}
            tick={chartStyles.axis.tick}
            axisLine={chartStyles.axis.axisLine}
            tickLine={chartStyles.axis.tickLine}
          />
          <YAxis
            tick={chartStyles.axis.tick}
            axisLine={chartStyles.axis.axisLine}
            tickLine={chartStyles.axis.tickLine}
          />
          <Tooltip
            content={<CustomTooltip />}
            cursor={chartStyles.tooltip.cursor}
          />
          {showLegend && <Legend {...chartStyles.legend} />}
          {categories.map((category, i) => (
            <Line
              key={category}
              type={curveType === 'smooth' ? 'monotone' : 'linear'}
              dataKey={category}
              stroke={chartColors[i % chartColors.length]}
              strokeWidth={chartConfigs.lineChart.strokeWidth}
              dot={{
                fill: chartColors[i % chartColors.length],
                strokeWidth: chartConfigs.lineChart.dot.strokeWidth,
                r: chartConfigs.lineChart.dot.r
              }}
              activeDot={{
                r: chartConfigs.lineChart.activeDot.r,
                stroke: chartColors[i % chartColors.length],
                strokeWidth: chartConfigs.lineChart.activeDot.strokeWidth,
                fill: '#ffffff'
              }}
              animationDuration={showAnimation ? chartConfigs.lineChart.animationDuration : 0}
            />
          ))}
        </RechartsLineChart>
      </ResponsiveContainer>
    </div>
  );
}

export function AreaChart({
  data,
  index,
  categories,
  colors,
  className,
  showAnimation = true,
  showLegend = true,
  showGridLines = true,
  valueFormatter = (value) => `${value}`,
  curveType = 'linear',
  fillOpacity = 0.3
}: AreaChartProps) {
  // Use theme colors if none provided
  const chartColors = colors || getChartColors(categories.length);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div style={chartStyles.tooltip.contentStyle}>
          <p className="text-vista-light font-medium mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {`${entry.dataKey}: ${valueFormatter(entry.value)}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className={cn("w-full h-full", className)}>
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={data}
          margin={chartConfigs.areaChart.margin}
        >
          <defs>
            {categories.map((category, i) => (
              <linearGradient key={category} id={`gradient-${category}`} x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor={chartColors[i % chartColors.length]} stopOpacity={fillOpacity} />
                <stop offset="95%" stopColor={chartColors[i % chartColors.length]} stopOpacity={0} />
              </linearGradient>
            ))}
          </defs>
          {showGridLines && (
            <CartesianGrid
              strokeDasharray={chartStyles.grid.strokeDasharray}
              stroke={chartStyles.grid.stroke}
              opacity={chartStyles.grid.opacity}
              horizontal={chartStyles.grid.horizontal}
              vertical={chartStyles.grid.vertical}
            />
          )}
          <XAxis
            dataKey={index}
            tick={chartStyles.axis.tick}
            axisLine={chartStyles.axis.axisLine}
            tickLine={chartStyles.axis.tickLine}
          />
          <YAxis
            tick={chartStyles.axis.tick}
            axisLine={chartStyles.axis.axisLine}
            tickLine={chartStyles.axis.tickLine}
          />
          <Tooltip
            content={<CustomTooltip />}
            cursor={chartStyles.tooltip.cursor}
          />
          {showLegend && <Legend {...chartStyles.legend} />}
          {categories.map((category, i) => (
            <Area
              key={category}
              type={curveType === 'smooth' ? 'monotone' : 'linear'}
              dataKey={category}
              stroke={chartColors[i % chartColors.length]}
              strokeWidth={chartConfigs.areaChart.strokeWidth}
              fill={`url(#gradient-${category})`}
              animationDuration={showAnimation ? chartConfigs.areaChart.animationDuration : 0}
            />
          ))}
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
}
          ))}
        </div>
      </div>
      
      {showLegend && (
        <div className="flex justify-center mt-4 flex-wrap gap-2">
          {categories.map((category, i) => (
            <div key={i} className="flex items-center">
              <div 
                className="w-3 h-3 mr-1 rounded-sm"
                style={{ backgroundColor: colors[i % colors.length] }}
              />
              <span className="text-xs text-vista-light/70">{category}</span>
            </div>
          ))}
        </div>
      )}
      
      <style jsx>{`
        @keyframes dash {
          to {
            stroke-dashoffset: 0;
          }
        }
      `}</style>
    </div>
  );
}

export function PieChart({
  data,
  index,
  category,
  colors,
  className,
  showAnimation = true,
  showLegend = true,
  valueFormatter = (value) => `${value}`,
  innerRadius = 0,
  outerRadius = 80
}: PieChartProps) {
  // Use theme colors if none provided
  const chartColors = colors || getChartColors(data.length);

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      return (
        <div style={chartStyles.tooltip.contentStyle}>
          <p className="text-vista-light font-medium mb-1">{data.name}</p>
          <p className="text-sm" style={{ color: data.color }}>
            {`Value: ${valueFormatter(data.value)}`}
          </p>
          <p className="text-sm text-vista-light/70">
            {`${((data.value / data.payload.total) * 100).toFixed(1)}%`}
          </p>
        </div>
      );
    }
    return null;
  };

  // Calculate total for percentage calculation
  const total = data.reduce((sum, item) => sum + Number(item[category]), 0);

  // Prepare data for Recharts
  const chartData = data.map((item, i) => ({
    name: item[index],
    value: Number(item[category]),
    total,
    fill: chartColors[i % chartColors.length]
  }));

  return (
    <div className={cn("w-full h-full", className)}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsPieChart margin={chartConfigs.pieChart.margin}>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            innerRadius={innerRadius || chartConfigs.pieChart.innerRadius}
            outerRadius={outerRadius || chartConfigs.pieChart.outerRadius}
            paddingAngle={chartConfigs.pieChart.paddingAngle}
            dataKey="value"
            animationDuration={showAnimation ? chartConfigs.pieChart.animationDuration : 0}
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.fill} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          {showLegend && <Legend {...chartStyles.legend} />}
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  );
}
