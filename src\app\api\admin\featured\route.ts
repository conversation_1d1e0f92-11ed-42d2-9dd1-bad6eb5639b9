import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/admin/featured
 * Get all featured content
 */
export async function GET(request: NextRequest) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the Content schema directly
    const ContentSchema = new mongoose.default.Schema({
      title: String,
      type: String,
      posterPath: String,
      backdropPath: String,
      year: Number,
      genres: [String],
      featured: Boolean
    }, {
      timestamps: true
    });

    // Get the Content model
    const Content = mongoose.default.models.Content ||
                   mongoose.default.model('Content', ContentSchema);

    // Define the FeaturedContent schema directly
    const FeaturedContentSchema = new mongoose.default.Schema({
      contentId: {
        type: mongoose.default.Schema.Types.ObjectId,
        ref: 'Content'
      },
      order: Number,
      startDate: Date,
      endDate: Date
    }, {
      timestamps: true
    });

    // Get the FeaturedContent model
    const FeaturedContent = mongoose.default.models.FeaturedContent ||
                           mongoose.default.model('FeaturedContent', FeaturedContentSchema);

    // Fetch featured content with content details
    const featuredContent = await FeaturedContent.find()
      .sort({ order: 1 })
      .populate('contentId', 'title type posterPath backdropPath year genres');

    // Transform data for response
    const transformedContent = await Promise.all(
      featuredContent.map(async (item: any) => {
        return {
          id: item._id.toString(),
          contentId: item.contentId._id.toString(),
          title: item.contentId.title,
          type: item.contentId.type,
          posterPath: item.contentId.posterPath,
          backdropPath: item.contentId.backdropPath,
          year: item.contentId.year,
          genres: item.contentId.genres,
          order: item.order,
          startDate: item.startDate,
          endDate: item.endDate
        };
      })
    );

    // Return featured content
    return NextResponse.json(transformedContent);
  } catch (error) {
    console.error('Error fetching featured content:', error);
    return NextResponse.json(
      { error: 'Failed to fetch featured content', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/featured
 * Add content to featured
 */
export async function POST(request: NextRequest) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const User = mongoose.default.models.User ||
                mongoose.default.model('User', new mongoose.default.Schema({
                  role: String
                }));

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the Content schema directly
    const ContentSchema = new mongoose.default.Schema({
      title: String,
      type: String,
      posterPath: String,
      backdropPath: String,
      year: Number,
      genres: [String],
      featured: Boolean
    }, {
      timestamps: true
    });

    // Get the Content model
    const Content = mongoose.default.models.Content ||
                   mongoose.default.model('Content', ContentSchema);

    // Define the FeaturedContent schema directly
    const FeaturedContentSchema = new mongoose.default.Schema({
      contentId: {
        type: mongoose.default.Schema.Types.ObjectId,
        ref: 'Content'
      },
      order: Number,
      startDate: Date,
      endDate: Date
    }, {
      timestamps: true
    });

    // Get the FeaturedContent model
    const FeaturedContent = mongoose.default.models.FeaturedContent ||
                           mongoose.default.model('FeaturedContent', FeaturedContentSchema);

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Get request data
    const data = await request.json();

    // Validate required fields
    if (!data.contentId) {
      return NextResponse.json({ error: 'Content ID is required' }, { status: 400 });
    }

    // Check if content exists
    const content = await Content.findById(data.contentId);
    if (!content) {
      return NextResponse.json({ error: 'Content not found' }, { status: 404 });
    }

    // Check if content is already featured
    const existingFeatured = await FeaturedContent.findOne({ contentId: data.contentId });
    if (existingFeatured) {
      return NextResponse.json({ error: 'Content is already featured' }, { status: 400 });
    }

    // Get highest order
    const highestOrder = await FeaturedContent.findOne().sort({ order: -1 });
    const newOrder = highestOrder ? highestOrder.order + 1 : 1;

    // Create new featured content
    const featuredContent = new FeaturedContent({
      contentId: data.contentId,
      order: data.order || newOrder,
      startDate: data.startDate,
      endDate: data.endDate
    });

    // Save featured content
    await featuredContent.save();

    // Update content to mark as featured
    await Content.findByIdAndUpdate(data.contentId, { featured: true });

    // Log admin activity directly
    await UserActivity.create({
      userId: new mongoose.default.Types.ObjectId(userId),
      type: 'admin',
      action: 'add_featured_content',
      details: `Admin added content to featured: ${content.title}`,
      ipAddress: request.ip || request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      timestamp: new Date(),
      metadata: { contentId: data.contentId }
    });

    // Return new featured content with content details
    const populatedFeatured = await FeaturedContent.findById(featuredContent._id)
      .populate('contentId', 'title type posterPath backdropPath year genres');

    return NextResponse.json({
      id: populatedFeatured._id.toString(),
      contentId: populatedFeatured.contentId._id.toString(),
      title: (populatedFeatured.contentId as any).title,
      type: (populatedFeatured.contentId as any).type,
      posterPath: (populatedFeatured.contentId as any).posterPath,
      backdropPath: (populatedFeatured.contentId as any).backdropPath,
      year: (populatedFeatured.contentId as any).year,
      genres: (populatedFeatured.contentId as any).genres,
      order: populatedFeatured.order,
      startDate: populatedFeatured.startDate,
      endDate: populatedFeatured.endDate
    }, { status: 201 });
  } catch (error) {
    console.error('Error adding featured content:', error);
    return NextResponse.json(
      { error: 'Failed to add featured content', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
