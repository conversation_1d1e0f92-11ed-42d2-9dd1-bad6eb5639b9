'use client';

import { useMemo, useCallback } from 'react';
import { Area, AreaChart, ResponsiveContainer, Tooltip, XAxis } from 'recharts';

interface SparklineChartProps {
  data: Array<{ date: string; value: number }>;
  color?: string;
  fillColor?: string;
  height?: number;
  showTooltip?: boolean;
  tooltipFormatter?: (value: number) => string;
  className?: string;
  animate?: boolean;
}

export function SparklineChart({
  data,
  color = "#3b82f6",
  fillColor,
  height = 40,
  showTooltip = true,
  tooltipFormatter,
  className = "",
  animate = true
}: SparklineChartProps) {
  // Generate a gradient ID unique to this component instance
  const gradientId = useMemo(() => `sparklineGradient-${Math.random().toString(36).substring(2, 9)}`, []);
  
  // Default gradient if no fill color specified
  const defaultGradient = fillColor ?? `url(#${gradientId})`;
  
  // Format tooltip values - wrap in useCallback to prevent recreation on every render
  const formatTooltipValue = useCallback((value: number) => {
    if (tooltipFormatter) {
      return tooltipFormatter(value);
    }
    return value.toLocaleString();
  }, [tooltipFormatter]);

  // Memoize the chart to prevent unnecessary re-renders
  const chartContent = useMemo(() => (
    <>
      <defs>
        <linearGradient id={gradientId} x1="0" y1="0" x2="0" y2="1">
          <stop offset="5%" stopColor={color} stopOpacity={0.3} />
          <stop offset="95%" stopColor={color} stopOpacity={0} />
        </linearGradient>
      </defs>
      <XAxis dataKey="date" hide />
      {showTooltip && (
        <Tooltip
          content={({ active, payload }) => {
            if (active && payload && payload.length) {
              return (
                <div className="rounded bg-background/80 backdrop-blur-sm border shadow px-2 py-1 text-xs">
                  {formatTooltipValue(payload[0].value as number)}
                </div>
              );
            }
            return null;
          }}
        />
      )}
      <Area
        type="monotone"
        dataKey="value"
        stroke={color}
        strokeWidth={1.5}
        fill={defaultGradient}
        animationDuration={animate ? 1500 : 0}
      />
    </>
  ), [
    color, 
    defaultGradient, 
    showTooltip, 
    formatTooltipValue, 
    animate, 
    gradientId
  ]);

  return (
    <div className={`w-full overflow-hidden ${className}`} style={{ height }}>
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={data}
          margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
        >
          {chartContent}
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
} 