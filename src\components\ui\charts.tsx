'use client';

import React, { useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

// Define a generic chart data type
interface ChartDataItem {
  [key: string]: string | number;
}

// Common props for all chart types
interface ChartProps {
  data: ChartDataItem[];
  index: string;
  className?: string;
  showAnimation?: boolean;
  showLegend?: boolean;
  showGridLines?: boolean;
  valueFormatter?: (value: number) => string;
}

// Bar chart specific props
interface BarChartProps extends ChartProps {
  categories: string[];
  colors?: string[];
  layout?: 'vertical' | 'horizontal';
}

// Line chart specific props
interface LineChartProps extends ChartProps {
  categories: string[];
  colors?: string[];
  curveType?: 'linear' | 'smooth';
}

// Pie chart specific props
interface PieChartProps {
  data: ChartDataItem[];
  index: string;
  category: string;
  colors?: string[];
  className?: string;
  showAnimation?: boolean;
  showLegend?: boolean;
  valueFormatter?: (value: number) => string;
}

// Simple placeholder implementation for charts
// In a real app, you would use a charting library like recharts, visx, or nivo

export function BarChart({
  data,
  index,
  categories,
  colors = ['#3b82f6', '#8b5cf6', '#ec4899', '#f97316', '#10b981'],
  className,
  showAnimation = true,
  showLegend = true,
  showGridLines = true,
  valueFormatter = (value) => `${value}`,
  layout = 'horizontal'
}: BarChartProps) {
  const chartRef = useRef<HTMLDivElement>(null);

  // This is a placeholder implementation
  // In a real app, you would render a proper chart here
  return (
    <div 
      ref={chartRef}
      className={cn("w-full h-full flex flex-col", className)}
    >
      <div className="flex-1 flex items-end">
        {data.map((item, i) => (
          <div key={i} className="flex flex-col items-center mx-1 h-full">
            <div className="flex-1 w-full flex items-end">
              {categories.map((category, j) => (
                <div
                  key={j}
                  className="mx-0.5 rounded-t-sm transition-all duration-500"
                  style={{
                    backgroundColor: colors[j % colors.length],
                    height: layout === 'horizontal' ? `${(Number(item[category]) / Math.max(...data.map(d => Number(d[category])))) * 100}%` : '20px',
                    width: layout === 'horizontal' ? '20px' : `${(Number(item[category]) / Math.max(...data.map(d => Number(d[category])))) * 100}%`,
                    opacity: showAnimation ? 0.7 : 1,
                    transform: showAnimation ? 'translateY(0)' : 'translateY(0)',
                    transition: showAnimation ? 'all 0.5s ease-out' : 'none'
                  }}
                  title={`${item[index]}: ${valueFormatter(Number(item[category]))}`}
                />
              ))}
            </div>
            <div className="text-xs mt-1 text-vista-light/70 truncate max-w-[60px] text-center">
              {item[index]}
            </div>
          </div>
        ))}
      </div>
      
      {showLegend && (
        <div className="flex justify-center mt-4 flex-wrap gap-2">
          {categories.map((category, i) => (
            <div key={i} className="flex items-center">
              <div 
                className="w-3 h-3 mr-1 rounded-sm"
                style={{ backgroundColor: colors[i % colors.length] }}
              />
              <span className="text-xs text-vista-light/70">{category}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export function LineChart({
  data,
  index,
  categories,
  colors = ['#3b82f6', '#8b5cf6', '#ec4899', '#f97316', '#10b981'],
  className,
  showAnimation = true,
  showLegend = true,
  showGridLines = true,
  valueFormatter = (value) => `${value}`,
  curveType = 'linear'
}: LineChartProps) {
  const chartRef = useRef<HTMLDivElement>(null);

  // This is a placeholder implementation
  // In a real app, you would render a proper chart here
  return (
    <div 
      ref={chartRef}
      className={cn("w-full h-full flex flex-col", className)}
    >
      <div className="flex-1 relative">
        {/* Grid lines */}
        {showGridLines && (
          <div className="absolute inset-0 flex flex-col justify-between">
            {[0, 1, 2, 3, 4].map((i) => (
              <div key={i} className="border-t border-vista-light/10 w-full h-0" />
            ))}
          </div>
        )}
        
        {/* Placeholder for line chart */}
        <div className="absolute inset-0 flex items-end">
          <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none">
            {categories.map((category, i) => {
              // Create a simple line path
              const points = data.map((item, j) => {
                const x = (j / (data.length - 1)) * 100;
                const y = 100 - ((Number(item[category]) / Math.max(...data.map(d => Number(d[category])))) * 100);
                return `${x},${y}`;
              }).join(' ');
              
              return (
                <polyline
                  key={i}
                  points={points}
                  fill="none"
                  stroke={colors[i % colors.length]}
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  style={{
                    opacity: showAnimation ? 0.7 : 1,
                    strokeDasharray: showAnimation ? '1000' : '0',
                    strokeDashoffset: showAnimation ? '1000' : '0',
                    animation: showAnimation ? 'dash 1.5s ease-in-out forwards' : 'none'
                  }}
                />
              );
            })}
          </svg>
        </div>
        
        {/* X-axis labels */}
        <div className="absolute bottom-0 left-0 right-0 flex justify-between">
          {data.map((item, i) => (
            <div key={i} className="text-xs text-vista-light/70 truncate max-w-[60px] text-center">
              {item[index]}
            </div>
          ))}
        </div>
      </div>
      
      {showLegend && (
        <div className="flex justify-center mt-4 flex-wrap gap-2">
          {categories.map((category, i) => (
            <div key={i} className="flex items-center">
              <div 
                className="w-3 h-3 mr-1 rounded-sm"
                style={{ backgroundColor: colors[i % colors.length] }}
              />
              <span className="text-xs text-vista-light/70">{category}</span>
            </div>
          ))}
        </div>
      )}
      
      <style jsx>{`
        @keyframes dash {
          to {
            stroke-dashoffset: 0;
          }
        }
      `}</style>
    </div>
  );
}

export function PieChart({
  data,
  index,
  category,
  colors = ['#3b82f6', '#8b5cf6', '#ec4899', '#f97316', '#10b981'],
  className,
  showAnimation = true,
  showLegend = true,
  valueFormatter = (value) => `${value}`
}: PieChartProps) {
  const chartRef = useRef<HTMLDivElement>(null);
  
  // Calculate total for percentages
  const total = data.reduce((sum, item) => sum + Number(item[category]), 0);
  
  // Calculate segments
  const segments = data.map((item, i) => {
    const value = Number(item[category]);
    const percentage = (value / total) * 100;
    return {
      name: item[index],
      value,
      percentage,
      color: colors[i % colors.length]
    };
  });
  
  // This is a placeholder implementation
  // In a real app, you would render a proper chart here
  return (
    <div 
      ref={chartRef}
      className={cn("w-full h-full flex flex-col", className)}
    >
      <div className="flex-1 flex justify-center items-center">
        <div className="relative w-32 h-32">
          {/* Render pie segments */}
          <svg width="100%" height="100%" viewBox="0 0 100 100">
            <circle cx="50" cy="50" r="40" fill="#1e293b" />
            
            {segments.map((segment, i) => {
              // For simplicity, we're just showing colored segments in a circle
              const angle = (segment.percentage / 100) * 360;
              const dashArray = 251.2; // Circumference of circle with r=40
              const dashOffset = dashArray - (dashArray * segment.percentage) / 100;
              
              return (
                <circle
                  key={i}
                  cx="50"
                  cy="50"
                  r="40"
                  fill="transparent"
                  stroke={segment.color}
                  strokeWidth="20"
                  strokeDasharray={dashArray}
                  strokeDashoffset={dashOffset}
                  transform="rotate(-90 50 50)"
                  style={{
                    opacity: showAnimation ? 0.7 : 1,
                    transition: showAnimation ? 'all 0.5s ease-out' : 'none',
                    transitionDelay: showAnimation ? `${i * 0.1}s` : '0s'
                  }}
                />
              );
            })}
          </svg>
        </div>
      </div>
      
      {showLegend && (
        <div className="flex justify-center mt-4 flex-wrap gap-2">
          {segments.map((segment, i) => (
            <div key={i} className="flex items-center">
              <div 
                className="w-3 h-3 mr-1 rounded-sm"
                style={{ backgroundColor: segment.color }}
              />
              <span className="text-xs text-vista-light/70">
                {segment.name} ({segment.percentage.toFixed(1)}%)
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
