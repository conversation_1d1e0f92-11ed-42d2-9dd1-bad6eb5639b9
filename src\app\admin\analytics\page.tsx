'use client';

import { useState, useEffect } from 'react';
import {
  BarChart3,
  TrendingUp,
  Users,
  Film,
  Tv,
  Clock,
  Calendar,
  RefreshCw,
  Download,
  Eye,
  Play,
  Heart,
  Activity
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useDashboardStats, useUserAnalytics } from '@/hooks/useAdminData';
import ContentAnalytics from '@/components/admin/ContentAnalytics';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

export default function AnalyticsPage() {
  const { user, isAdmin } = useAuth();
  const router = useRouter();

  // Redirect non-admin users
  useEffect(() => {
    if (user && !isAdmin()) {
      router.push('/');
    }
  }, [user, isAdmin, router]);
  const [timeRange, setTimeRange] = useState('7d');

  // Fetch analytics data
  const {
    data: statsData,
    isLoading: statsLoading,
    refetch: refetchStats
  } = useDashboardStats();

  const {
    data: userAnalyticsData,
    isLoading: userAnalyticsLoading,
    refetch: refetchUserAnalytics
  } = useUserAnalytics();

  // Content analytics is now handled by the ContentAnalytics component

  // Determine if any data is loading
  const isLoading = statsLoading || userAnalyticsLoading;

  // Refresh all analytics data
  const refreshAnalytics = () => {
    refetchStats();
    refetchUserAnalytics();
  };

  // Extract data or use defaults
  const overview = statsData ? {
    totalUsers: statsData.users.total,
    activeUsers: statsData.users.active,
    totalMovies: statsData.content.movies,
    totalShows: statsData.content.shows,
    totalWatchTime: statsData.views.total || 0,
    avgSessionDuration: statsData.views.dailyAverage || 0
  } : {
    totalUsers: 0,
    activeUsers: 0,
    totalMovies: 0,
    totalShows: 0,
    totalWatchTime: 0,
    avgSessionDuration: 0
  };

  const userActivity = userAnalyticsData ? {
    dailyActiveUsers: userAnalyticsData.dailyActiveUsers || [],
    newSignups: userAnalyticsData.dailySignups || [],
    deviceDistribution: userAnalyticsData.deviceDistribution || {},
    geographicDistribution: userAnalyticsData.geographicDistribution || {}
  } : {
    dailyActiveUsers: [],
    newSignups: [],
    deviceDistribution: {},
    geographicDistribution: {}
  };

  // Content analytics data is now handled by the ContentAnalytics component

  // Engagement data - we'll use real data from the ContentAnalytics component
  // This is just a placeholder for the UI structure
  const engagement = statsData?.popular ? {
    watchlistAdditions: statsData.popular.map(item => item.views || 0).slice(0, 7),
    socialShares: statsData.popular.map(item => Math.floor((item.views || 0) * 0.1)).slice(0, 7),
    ratings: statsData.popular.map(item => Math.floor((item.views || 0) * 0.2)).slice(0, 7),
    comments: statsData.popular.map(item => Math.floor((item.views || 0) * 0.05)).slice(0, 7)
  } : {
    watchlistAdditions: [0, 0, 0, 0, 0, 0, 0],
    socialShares: [0, 0, 0, 0, 0, 0, 0],
    ratings: [0, 0, 0, 0, 0, 0, 0],
    comments: [0, 0, 0, 0, 0, 0, 0]
  };

  // Format large numbers
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US', {
      notation: num > 9999 ? "compact" : "standard",
      compactDisplay: "short"
    }).format(num);
  };

  // Format time in hours and minutes
  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;

    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(0)}%`;
  };



  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-vista-light">Analytics Dashboard</h1>
          <p className="text-vista-light/70">
            Insights into user activity and content performance
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Time Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="24h">Last 24 Hours</SelectItem>
              <SelectItem value="7d">Last 7 Days</SelectItem>
              <SelectItem value="30d">Last 30 Days</SelectItem>
              <SelectItem value="90d">Last 90 Days</SelectItem>
              <SelectItem value="1y">Last Year</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={refreshAnalytics} disabled={isLoading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? 'Loading...' : 'Refresh'}
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-vista-light text-lg">Total Users</CardTitle>
            <CardDescription>All registered users</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Users className="h-8 w-8 text-vista-blue mr-3" />
              <div>
                <div className="text-3xl font-bold text-vista-light">{formatNumber(overview.totalUsers)}</div>
                <p className="text-vista-light/70 text-sm">
                  <span className="text-green-500">{formatNumber(overview.activeUsers)}</span> active users
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-vista-light text-lg">Content Library</CardTitle>
            <CardDescription>Movies and TV shows</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Film className="h-8 w-8 text-vista-blue mr-3" />
              <div>
                <div className="text-3xl font-bold text-vista-light">
                  {formatNumber(overview.totalMovies + overview.totalShows)}
                </div>
                <p className="text-vista-light/70 text-sm">
                  <span className="text-vista-blue">{formatNumber(overview.totalMovies)}</span> movies,
                  <span className="text-vista-blue ml-1">{formatNumber(overview.totalShows)}</span> shows
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-vista-light text-lg">Total Watch Time</CardTitle>
            <CardDescription>Across all users</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-vista-blue mr-3" />
              <div>
                <div className="text-3xl font-bold text-vista-light">
                  {formatNumber(Math.floor(overview.totalWatchTime / 60))}h
                </div>
                <p className="text-vista-light/70 text-sm">
                  Avg. session: {formatTime(overview.avgSessionDuration)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for different analytics views */}
      <Tabs defaultValue="user-activity" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="user-activity" className="flex items-center">
            <Users className="mr-2 h-4 w-4" />
            User Activity
          </TabsTrigger>
          <TabsTrigger value="content-performance" className="flex items-center">
            <BarChart3 className="mr-2 h-4 w-4" />
            Content Performance
          </TabsTrigger>
          <TabsTrigger value="engagement" className="flex items-center">
            <Activity className="mr-2 h-4 w-4" />
            Engagement
          </TabsTrigger>
          <TabsTrigger value="content-analytics" className="flex items-center">
            <Film className="mr-2 h-4 w-4" />
            Content Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="user-activity" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-vista-light">Daily Active Users</CardTitle>
                <CardDescription>Last 7 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[250px] flex items-end space-x-2">
                  {userActivity.dailyActiveUsers.map((value, index) => {
                    const maxValue = Math.max(...userActivity.dailyActiveUsers, 1);
                    const height = (value / maxValue) * 100;
                    return (
                      <div key={index} className="flex-1 flex flex-col items-center">
                        <div
                          className="w-full bg-vista-blue rounded-t-sm"
                          style={{ height: `${height}%` }}
                        ></div>
                        <span className="text-vista-light/70 text-xs mt-2">
                          {new Date(Date.now() - (6 - index) * 86400000).toLocaleDateString('en-US', { weekday: 'short' })}
                        </span>
                        <span className="text-vista-light/90 text-xs font-medium">
                          {formatNumber(value)}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-vista-light">New Signups</CardTitle>
                <CardDescription>Last 7 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[250px] flex items-end space-x-2">
                  {userActivity.newSignups.map((value, index) => {
                    const maxValue = Math.max(...userActivity.newSignups, 1);
                    const height = (value / maxValue) * 100;
                    return (
                      <div key={index} className="flex-1 flex flex-col items-center">
                        <div
                          className="w-full bg-green-500 rounded-t-sm"
                          style={{ height: `${height}%` }}
                        ></div>
                        <span className="text-vista-light/70 text-xs mt-2">
                          {new Date(Date.now() - (6 - index) * 86400000).toLocaleDateString('en-US', { weekday: 'short' })}
                        </span>
                        <span className="text-vista-light/90 text-xs font-medium">
                          {formatNumber(value)}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-vista-light">Device Distribution</CardTitle>
                <CardDescription>User device types</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(userActivity.deviceDistribution).map(([device, percentage]) => (
                    <div key={device} className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span className="capitalize text-vista-light">{device}</span>
                        <span className="text-vista-light/70">{percentage}%</span>
                      </div>
                      <div className="w-full bg-vista-dark-lighter rounded-full h-2">
                        <div
                          className="bg-vista-blue h-2 rounded-full"
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-vista-light">Geographic Distribution</CardTitle>
                <CardDescription>User locations</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(userActivity.geographicDistribution).map(([region, percentage]) => (
                    <div key={region} className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span className="text-vista-light">{region}</span>
                        <span className="text-vista-light/70">{percentage}%</span>
                      </div>
                      <div className="w-full bg-vista-dark-lighter rounded-full h-2">
                        <div
                          className="bg-vista-blue h-2 rounded-full"
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="content-performance" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-vista-light">Top Movies</CardTitle>
                <CardDescription>By views</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {contentAnalyticsData.topMovies.map((movie, index) => (
                    <div key={index} className="flex items-center justify-between p-2 rounded-md bg-vista-dark">
                      <div className="flex items-center">
                        <span className="text-vista-light/50 mr-3 w-5 text-center">{index + 1}</span>
                        <div>
                          <p className="text-vista-light font-medium">{movie.title}</p>
                          <div className="flex items-center text-xs text-vista-light/70">
                            <Eye className="h-3 w-3 mr-1" />
                            {formatNumber(movie.views)} views
                            <span className="mx-1">•</span>
                            <Clock className="h-3 w-3 mr-1" />
                            {formatTime(movie.avgWatchTime)}
                          </div>
                        </div>
                      </div>
                      <Badge variant="outline" className="bg-vista-blue/10 text-vista-blue">
                        {formatPercentage(movie.completionRate)}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-vista-light">Top TV Shows</CardTitle>
                <CardDescription>By views</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {contentAnalyticsData.topShows.map((show, index) => (
                    <div key={index} className="flex items-center justify-between p-2 rounded-md bg-vista-dark">
                      <div className="flex items-center">
                        <span className="text-vista-light/50 mr-3 w-5 text-center">{index + 1}</span>
                        <div>
                          <p className="text-vista-light font-medium">{show.title}</p>
                          <div className="flex items-center text-xs text-vista-light/70">
                            <Eye className="h-3 w-3 mr-1" />
                            {formatNumber(show.views)} views
                            <span className="mx-1">•</span>
                            <Clock className="h-3 w-3 mr-1" />
                            {formatTime(show.avgWatchTime)}/ep
                          </div>
                        </div>
                      </div>
                      <Badge variant="outline" className="bg-vista-blue/10 text-vista-blue">
                        {formatPercentage(show.completionRate)}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-vista-light">Genre Popularity</CardTitle>
                <CardDescription>Percentage of total views</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(contentAnalyticsData.genrePopularity).map(([genre, percentage]) => (
                    <div key={genre} className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span className="text-vista-light">{genre}</span>
                        <span className="text-vista-light/70">{percentage}%</span>
                      </div>
                      <div className="w-full bg-vista-dark-lighter rounded-full h-2">
                        <div
                          className="bg-vista-blue h-2 rounded-full"
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-vista-light">Watch Time by Hour</CardTitle>
                <CardDescription>24-hour distribution</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[250px] flex items-end space-x-0">
                  {contentAnalyticsData.watchTimeByHour.map((hourData, index) => {
                    const maxValue = Math.max(...contentAnalyticsData.watchTimeByHour.map(item => item.value));
                    const height = (hourData.value / maxValue) * 100;
                    return (
                      <div
                        key={index}
                        className="relative w-8 h-full flex flex-col justify-end group"
                      >
                        <div
                          className="w-6 bg-vista-blue rounded-t-sm"
                          style={{ height: `${height}%` }}
                        ></div>
                        <span className="text-xs text-vista-light/50 mt-1">
                          {hourData.hour}h
                        </span>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="engagement" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-vista-light">Watchlist Additions</CardTitle>
                <CardDescription>Last 7 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[250px] flex items-end space-x-2">
                  {engagement.watchlistAdditions.map((value, index) => {
                    const maxValue = Math.max(...engagement.watchlistAdditions, 1);
                    const height = (value / maxValue) * 100;
                    return (
                      <div key={index} className="flex-1 flex flex-col items-center">
                        <div
                          className="w-full bg-purple-500 rounded-t-sm"
                          style={{ height: `${height}%` }}
                        ></div>
                        <span className="text-vista-light/70 text-xs mt-2">
                          {new Date(Date.now() - (6 - index) * 86400000).toLocaleDateString('en-US', { weekday: 'short' })}
                        </span>
                        <span className="text-vista-light/90 text-xs font-medium">
                          {formatNumber(value)}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-vista-light">Social Engagement</CardTitle>
                <CardDescription>Last 7 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[250px] flex items-end space-x-2">
                  {[0, 1, 2, 3, 4, 5, 6].map((dayIndex) => {
                    const shares = engagement.socialShares[dayIndex];
                    const ratings = engagement.ratings[dayIndex];
                    const comments = engagement.comments[dayIndex];
                    const total = shares + ratings + comments;
                    const maxTotal = Math.max(...engagement.socialShares.map((v, i) =>
                      v + engagement.ratings[i] + engagement.comments[i]
                    ), 1);
                    const height = (total / maxTotal) * 100;

                    // Calculate proportions
                    const sharesHeight = (shares / total) * height;
                    const ratingsHeight = (ratings / total) * height;
                    const commentsHeight = (comments / total) * height;

                    return (
                      <div key={dayIndex} className="flex-1 flex flex-col items-center">
                        <div className="w-full flex flex-col-reverse">
                          <div
                            className="w-full bg-blue-500 rounded-t-sm"
                            style={{ height: `${sharesHeight}%` }}
                            title={`Shares: ${shares}`}
                          ></div>
                          <div
                            className="w-full bg-yellow-500 rounded-t-sm"
                            style={{ height: `${ratingsHeight}%` }}
                            title={`Ratings: ${ratings}`}
                          ></div>
                          <div
                            className="w-full bg-green-500 rounded-t-sm"
                            style={{ height: `${commentsHeight}%` }}
                            title={`Comments: ${comments}`}
                          ></div>
                        </div>
                        <span className="text-vista-light/70 text-xs mt-2">
                          {new Date(Date.now() - (6 - dayIndex) * 86400000).toLocaleDateString('en-US', { weekday: 'short' })}
                        </span>
                      </div>
                    );
                  })}
                </div>
                <div className="flex justify-center mt-4 space-x-4">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-blue-500 rounded-sm mr-2"></div>
                    <span className="text-vista-light/70 text-sm">Shares</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-yellow-500 rounded-sm mr-2"></div>
                    <span className="text-vista-light/70 text-sm">Ratings</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-500 rounded-sm mr-2"></div>
                    <span className="text-vista-light/70 text-sm">Comments</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="content-analytics" className="space-y-4">
          <ContentAnalytics />
        </TabsContent>
      </Tabs>
    </div>
  );
}
