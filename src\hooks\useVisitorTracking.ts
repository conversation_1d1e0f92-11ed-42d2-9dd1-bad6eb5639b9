import { useEffect, useState, useRef } from 'react';
import { usePathname } from 'next/navigation';

/**
 * Hook to track anonymous visitors
 *
 * This hook:
 * 1. Tracks first-time visitors
 * 2. Updates returning visitors
 * 3. Tracks page views when pathname changes
 * 4. Provides visitor ID for other components
 * 5. Collects browser fingerprint data to prevent duplicates
 */
export function useVisitorTracking() {
  const [visitorId, setVisitorId] = useState<string | null>(null);
  const [isTracked, setIsTracked] = useState(false);
  const pathname = usePathname();
  const previousPathname = useRef<string | null>(null);

  // Track visitor on initial page load only
  useEffect(() => {
    const trackVisitor = async () => {
      try {
        // Get referrer (if available)
        const referrer = document.referrer || null;

        // Collect browser fingerprint data
        const browserInfo = getBrowserFingerprint();

        // Send tracking data to API
        const response = await fetch('/api/tracking/visitor', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            referrer,
            browserInfo,
          }),
        });

        if (response.ok) {
          const data = await response.json();
          setVisitorId(data.visitorId);
          setIsTracked(true);
        }
      } catch (error) {
        console.error('Error tracking visitor:', error);
      }
    };

    // Only track if not already tracked
    if (!isTracked) {
      trackVisitor();
    }
  }, [isTracked]);

  // Track page views when pathname changes
  useEffect(() => {
    const trackPageView = async () => {
      // Only track if pathname has changed (not on initial load)
      if (previousPathname.current !== null && previousPathname.current !== pathname) {
        try {
          // Send page view data to API
          await fetch('/api/tracking/pageview', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              path: pathname,
            }),
          });
        } catch (error) {
          console.error('Error tracking page view:', error);
        }
      }

      // Update previous pathname
      previousPathname.current = pathname;
    };

    // Only track page views if visitor is already tracked
    if (isTracked && visitorId) {
      trackPageView();
    }
  }, [pathname, visitorId, isTracked]);

  return { visitorId, isTracked };
}

/**
 * Collects browser-specific information to create a more accurate fingerprint
 * This doesn't use invasive fingerprinting techniques, just basic browser properties
 */
function getBrowserFingerprint() {
  try {
    const screen = window.screen;
    const nav = window.navigator;

    // Get device memory if available (Chrome only)
    const memory = (nav as any).deviceMemory;

    // Get hardware concurrency (available in most modern browsers)
    const cpuCores = nav.hardwareConcurrency;

    // Get device pixel ratio (screen density)
    const pixelRatio = window.devicePixelRatio;

    // Get connection type if available
    const connection = (nav as any).connection ||
                      (nav as any).mozConnection ||
                      (nav as any).webkitConnection;

    // Get touch support
    const touchPoints = nav.maxTouchPoints || 0;
    const touchSupport = touchPoints > 0;

    // Get mobile-specific info
    const isMobile = /Mobi|Android|iPhone|iPad|iPod/i.test(nav.userAgent);

    // Get browser vendor-specific info
    const vendor = nav.vendor || '';

    // Get screen orientation if available
    let orientation = '';
    try {
      orientation = screen.orientation?.type || '';
    } catch (e) {
      // Ignore errors for browsers that don't support this
    }

    return {
      // Basic screen properties
      screenWidth: screen?.width,
      screenHeight: screen?.height,
      colorDepth: screen?.colorDepth,
      pixelRatio,
      orientation,

      // Timezone and language
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: nav.language,

      // Platform and hardware info
      platform: nav.platform,
      memory,
      cpuCores,

      // Connection info
      connectionType: connection?.type,
      effectiveType: connection?.effectiveType,

      // Device capabilities
      touchSupport,
      touchPoints,

      // Browser settings
      cookiesEnabled: navigator.cookieEnabled,
      doNotTrack: nav.doNotTrack,

      // Mobile detection
      isMobile,
      vendor,

      // Additional browser capabilities
      pdfViewerEnabled: (nav as any).pdfViewerEnabled,

      // Date of fingerprint collection (helps with time-based analysis)
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error collecting browser fingerprint:', error);
    return {};
  }
}

export default useVisitorTracking;
